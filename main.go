package main

import (
	"context"
	"fmt"

	"backend/firebase"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/auth"
	"google.golang.org/api/option"
)

func initializeFirebase() (*auth.Client, error) {
	ctx := context.Background()
	// Path to your service account key JSON file
	opt := option.WithCredentialsFile("path/to/serviceAccountKey.json")
	app, err := firebase.NewApp(ctx, nil, opt)
	if err != nil {
		return nil, err
	}

	authClient, err := app.Auth(ctx)
	if err != nil {
		return nil, err
	}

	return authClient, nil
}

func verifyIDToken(authClient *auth.Client, idToken string) (*auth.Token, error) {
	ctx := context.Background()
	token, err := authClient.VerifyIDToken(ctx, idToken)
	if err != nil {
		return nil, err
	}
	return token, nil
}

func main() {
	CreateUser()

	fmt.Printf("hello")
}
